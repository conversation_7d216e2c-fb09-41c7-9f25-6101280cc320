import { useState } from 'react';

export const AICheck = () => {
  if (typeof window === 'undefined') return null;

  const [isOpen, setIsOpen] = useState(false);
  const [isCopied, setIsCopied] = useState(false);

  const currentUrl = window.location.origin + window.location.pathname + window.location.search + window.location.hash;

  const prompt = `Please analyze this documentation page and help me with questions about it. Page URL: ${currentUrl}\n\nTasks:\n- Summarize the key points succinctly\n- Explain any important caveats\n- Answer follow-up questions using only this page unless I ask to search more`;

  const openVendor = (vendor) => {
    const encodedPrompt = encodeURIComponent(prompt);

    // Best-effort deep links; some UIs may ignore query params. We still copy to clipboard.
    const vendorUrlMap = {
      chatgpt: `https://chatgpt.com/?q=${encodedPrompt}`,
      gemini: `https://gemini.google.com/app?hl=en&q=${encodedPrompt}`,
      claude: `https://claude.ai/new`,
    };

    try {
      navigator.clipboard.writeText(prompt).then(() => {
        setIsCopied(true);
        setTimeout(() => setIsCopied(false), 2000);
      }).catch(() => {
        // no-op if clipboard fails
      });
    } catch (_) {
      // clipboard may fail in some browsers; ignore
    }

    const href = vendorUrlMap[vendor] || vendorUrlMap.chatgpt;
    window.open(href, '_blank', 'noopener,noreferrer');
    setIsOpen(false);
  };

  return (
    <div style={{ position: 'fixed', bottom: '1.25rem', right: '1.25rem', zIndex: 50 }}>
      <button
        onClick={() => setIsOpen(true)}
        style={{
          display: 'inline-flex',
          alignItems: 'center',
          gap: '0.5rem',
          background: 'linear-gradient(135deg, #1D4ED8 0%, #3B82F6 100%)',
          color: 'white',
          border: 'none',
          padding: '0.6rem 1rem',
          borderRadius: '9999px',
          boxShadow: '0 10px 20px rgba(29,78,216,0.25)',
          cursor: 'pointer',
          fontWeight: 600
        }}
        aria-label="Check with AI"
      >
        <span style={{ display: 'inline-flex', width: 18, height: 18, borderRadius: 9999, background: 'white', color: '#1D4ED8', alignItems: 'center', justifyContent: 'center', fontSize: 12 }}>AI</span>
        Check with AI
      </button>

      {isOpen && (
        <div
          role="dialog"
          aria-modal="true"
          onClick={() => setIsOpen(false)}
          style={{
            position: 'fixed', inset: 0, background: 'rgba(0,0,0,0.35)',
            display: 'flex', alignItems: 'center', justifyContent: 'center'
          }}
        >
          <div
            onClick={(e) => e.stopPropagation()}
            style={{
              width: '100%', maxWidth: 520, background: 'white', color: '#0F172A',
              borderRadius: 12, boxShadow: '0 20px 40px rgba(0,0,0,0.25)', padding: '1rem 1rem'
            }}
          >
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '0.5rem' }}>
              <h3 style={{ fontSize: '1.1rem', margin: 0, fontWeight: 700 }}>Ask an AI about this page</h3>
              <button onClick={() => setIsOpen(false)} aria-label="Close" style={{ background: 'transparent', border: 'none', fontSize: 20, cursor: 'pointer' }}>×</button>
            </div>

            <p style={{ marginTop: 0, marginBottom: '0.5rem', color: '#334155' }}>
              We’ll copy a short prompt with this page’s URL. Choose a model below. A new tab will open—paste if the prompt isn’t auto-filled.
            </p>

            <div style={{
              display: 'grid', gridTemplateColumns: 'repeat(3, minmax(0, 1fr))', gap: '0.5rem', marginBottom: '0.75rem'
            }}>
              <button onClick={() => openVendor('chatgpt')} style={vendorButtonStyle('#10B981')}>ChatGPT</button>
              <button onClick={() => openVendor('claude')} style={vendorButtonStyle('#8B5CF6')}>Claude</button>
              <button onClick={() => openVendor('gemini')} style={vendorButtonStyle('#F59E0B')}>Gemini</button>
            </div>

            <div style={{ background: '#F8FAFC', border: '1px solid #E2E8F0', borderRadius: 8, padding: '0.5rem', maxHeight: 140, overflow: 'auto' }}>
              <pre style={{ margin: 0, whiteSpace: 'pre-wrap', wordBreak: 'break-word', fontSize: 12, lineHeight: 1.4 }}>{prompt}</pre>
            </div>

            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginTop: '0.5rem' }}>
              <button
                onClick={() => {
                  try {
                    navigator.clipboard.writeText(prompt).then(() => {
                      setIsCopied(true);
                      setTimeout(() => setIsCopied(false), 2000);
                    });
                  } catch (_) {}
                }}
                style={{ background: '#0EA5E9', color: 'white', border: 'none', padding: '0.5rem 0.75rem', borderRadius: 8, cursor: 'pointer', fontWeight: 600 }}
              >
                Copy prompt
              </button>
              {isCopied && <span style={{ color: '#16A34A', fontSize: 12 }}>Copied to clipboard</span>}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

const vendorButtonStyle = (bg) => ({
  background: bg,
  color: 'white',
  border: 'none',
  padding: '0.5rem 0.75rem',
  borderRadius: 8,
  cursor: 'pointer',
  fontWeight: 700
});

export default function Snippet() {
  return <AICheck />;
}


